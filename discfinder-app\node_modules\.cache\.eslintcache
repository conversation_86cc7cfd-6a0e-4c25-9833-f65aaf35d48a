[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx": "7", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx": "8", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx": "9"}, {"size": 554, "mtime": 1751073000293, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1751073000018, "results": "12", "hashOfConfig": "11"}, {"size": 41080, "mtime": 1751120047496, "results": "13", "hashOfConfig": "11"}, {"size": 16772, "mtime": 1751118576518, "results": "14", "hashOfConfig": "11"}, {"size": 8091, "mtime": 1751118047058, "results": "15", "hashOfConfig": "11"}, {"size": 6258, "mtime": 1751083217163, "results": "16", "hashOfConfig": "11"}, {"size": 4373, "mtime": 1751086289968, "results": "17", "hashOfConfig": "11"}, {"size": 17733, "mtime": 1751120034997, "results": "18", "hashOfConfig": "11"}, {"size": 15916, "mtime": 1751119931275, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["47", "48", "49", "50"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx", ["51", "52"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx", ["53", "54"], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 283, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 283, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "56", "line": 297, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 297, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "56", "line": 375, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 375, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "56", "line": 416, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 416, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "59", "line": 73, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 73, "endColumn": 22}, {"ruleId": "55", "severity": 1, "message": "56", "line": 99, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 99, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "56", "line": 81, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 81, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "59", "line": 120, "column": 15, "nodeType": "57", "messageId": "58", "endLine": 120, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "'success' is assigned a value but never used."]